import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

/**
 * 获取用户的住宿预订记录
 * GET /api/accommodations/bookings/[user_id]
 */
export async function GET(request: NextRequest, context: { params: Promise<{ user_id: string }> }) {
  try {
    const { user_id } = await context.params

    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to view your bookings",
        },
        { status: 401 }
      )
    }

    // 验证用户ID匹配 (确保用户只能查看自己的预订)
    // 在实际应用中，应该从JWT token中获取用户ID并与路径参数比较
    // TODO: 实现用户ID验证逻辑

    // 在实际应用中，这里应该从数据库查询用户的预订记录
    // 目前返回模拟数据
    // TODO: 实现数据库查询逻辑

    // 模拟用户预订记录 - 现在显示一些临时数据
    const hasBookings = true // 设置为有预订，显示临时数据

    if (!hasBookings) {
      return NextResponse.json(
        {
          code: 200,
          msg: "Bookings retrieved successfully",
          message: "No bookings found for this user",
          data: [],
        },
        { status: 200 }
      )
    }

    // 模拟返回预订数据 - 添加多个预订示例
    const mockBookings = [
      {
        booking_id: `booking_${user_id}_001`,
        user_id: parseInt(user_id),
        hotel_id: 1,
        hotel_name: "Huazhong Agricultural University International Academic Exchange Center（IAEC）",
        room_type: "Standard Room",
        check_in_date: "2025-10-16",
        check_out_date: "2025-10-19",
        guests_count: 2,
        companions: ["john_doe", "jane_smith"],
        total_price: 804, // 268 * 3 nights
        currency: "CNY",
        status: "confirmed" as const,
        special_requests: "Non-smoking room preferred, early check-in if possible",
        booking_time: "2025-01-15T10:30:00.000Z",
        confirmation_number: "IFMB2025-001",
      },
      {
        booking_id: `booking_${user_id}_002`,
        user_id: parseInt(user_id),
        hotel_id: 2,
        hotel_name: "Wuhan Nanhu Huanong Intercity Hotel",
        room_type: "King Room",
        check_in_date: "2025-10-15",
        check_out_date: "2025-10-20",
        guests_count: 1,
        companions: [],
        total_price: 1900, // 380 * 5 nights
        currency: "CNY",
        status: "pending" as const,
        special_requests: "",
        booking_time: "2025-01-20T14:15:00.000Z",
        confirmation_number: "IFMB2025-002",
      },
    ]

    return NextResponse.json(
      {
        code: 200,
        msg: "Bookings retrieved successfully",
        message: "User bookings retrieved successfully",
        data: mockBookings,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get user bookings error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while retrieving your bookings. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
