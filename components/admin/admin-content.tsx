"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

export default function AdminContentPage() {
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)
    // 模拟保存操作
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage website content and information</p>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? (
            <>
              <i className="fas fa-spinner fa-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="fas fa-save mr-2"></i>
              Save Changes
            </>
          )}
        </Button>
      </div>

      {/* 内容管理标签页 */}
      <Tabs defaultValue="homepage" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="homepage">Homepage</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
          <TabsTrigger value="speakers">Speakers</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="homepage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Homepage Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="hero-title">Hero Section Title</Label>
                <Input id="hero-title" defaultValue="International Forum on Maize Biology" className="mt-1" />
              </div>
              <div>
                <Label htmlFor="hero-subtitle">Hero Section Subtitle</Label>
                <Input id="hero-subtitle" defaultValue="IFMB 2025" className="mt-1" />
              </div>
              <div>
                <Label htmlFor="hero-description">Hero Section Description</Label>
                <Textarea
                  id="hero-description"
                  defaultValue="Join us for the premier international conference on maize biology research."
                  className="mt-1"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>About Section</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="about-content">About Content</Label>
                <Textarea
                  id="about-content"
                  defaultValue="The International Forum on Maize Biology (IFMB) is a premier scientific conference..."
                  className="mt-1"
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="speakers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Speaker Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600">Manage keynote speakers and their information.</p>
              <Button>
                <i className="fas fa-plus mr-2"></i>
                Add New Speaker
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Schedule Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600">Manage conference schedule and sessions.</p>
              <Button>
                <i className="fas fa-plus mr-2"></i>
                Add New Session
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
