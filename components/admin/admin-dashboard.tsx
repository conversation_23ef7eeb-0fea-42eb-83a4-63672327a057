"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

// 模拟统计数据
type Stats = {
  totalUsers: number
  totalRegistrations: number
  totalSpeakers: number
  pendingApprovals: number
}

// 模拟用户数据
const mockUsers = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", role: "user" as const, registeredAt: "2024-01-15" },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", role: "user" as const, registeredAt: "2024-01-16" },
  { id: "3", name: "Admin User", email: "<EMAIL>", role: "admin" as const, registeredAt: "2024-01-10" },
  { id: "4", name: "<PERSON><PERSON> <PERSON>", email: "<EMAIL>", role: "user" as const, registeredAt: "2024-01-17" },
  { id: "5", name: "Prof. <PERSON>", email: "<EMAIL>", role: "user" as const, registeredAt: "2024-01-18" },
]

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    totalUsers: 0,
    totalRegistrations: 0,
    totalSpeakers: 0,
    pendingApprovals: 0,
  })
  const [users] = useState(mockUsers)

  useEffect(() => {
    // 模拟加载统计数据
    setStats({
      totalUsers: mockUsers.length,
      totalRegistrations: mockUsers.length - 1, // 排除管理员
      totalSpeakers: 15,
      pendingApprovals: 3,
    })
  }, [])

  return (
    <div>
      {/* 统计卡片 */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.totalUsers}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-users text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Registrations</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.totalRegistrations}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-user-check text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Speakers</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.totalSpeakers}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <i className="fas fa-microphone text-xl text-amber-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Pending Approvals</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.pendingApprovals}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <i className="fas fa-clock text-xl text-red-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 内容标签页 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Recent Users</TabsTrigger>
          <TabsTrigger value="speakers">Speakers</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Welcome to the Admin Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                This is the administrative dashboard for the International Forum on Maize Biology 2025. From here, you
                can manage users, speakers, content, and settings for the conference.
              </p>
              <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                <Card>
                  <CardContent className="p-4">
                    <h4 className="mb-2 font-semibold text-gray-900">Quick Actions</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start">
                        <i className="fas fa-user-plus mr-2"></i>
                        Add New User
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <i className="fas fa-microphone-alt mr-2"></i>
                        Add Speaker
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <i className="fas fa-envelope mr-2"></i>
                        Send Announcement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <h4 className="mb-2 font-semibold text-gray-900">System Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Database</span>
                        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                          <i className="fas fa-check-circle mr-1"></i> Operational
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">API Services</span>
                        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                          <i className="fas fa-check-circle mr-1"></i> Operational
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Storage</span>
                        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                          <i className="fas fa-check-circle mr-1"></i> 85% Available
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Name</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Email</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Role</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Registered</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="px-4 py-3">{user.name}</td>
                        <td className="px-4 py-3">{user.email}</td>
                        <td className="px-4 py-3">
                          <Badge
                            variant="outline"
                            className={
                              user.role === "admin"
                                ? "border-purple-200 bg-purple-50 text-purple-700"
                                : "border-blue-200 bg-blue-50 text-blue-700"
                            }
                          >
                            {user.role}
                          </Badge>
                        </td>
                        <td className="px-4 py-3">{user.registeredAt}</td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <i className="fas fa-edit text-gray-500"></i>
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <i className="fas fa-trash text-red-500"></i>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="speakers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Speaker Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600">Manage speakers for the International Forum on Maize Biology 2025.</p>
              <Button>
                <i className="fas fa-plus mr-2"></i>
                Add New Speaker
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600">Configure system settings for the admin dashboard.</p>
              <div className="space-y-4">
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">Notifications</h4>
                  <div className="flex items-center justify-between border-b border-gray-100 py-2">
                    <span className="text-gray-600">Email notifications</span>
                    <Button variant="outline" size="sm">
                      Enabled
                    </Button>
                  </div>
                  <div className="flex items-center justify-between border-b border-gray-100 py-2">
                    <span className="text-gray-600">System alerts</span>
                    <Button variant="outline" size="sm">
                      Enabled
                    </Button>
                  </div>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">Security</h4>
                  <div className="flex items-center justify-between border-b border-gray-100 py-2">
                    <span className="text-gray-600">Two-factor authentication</span>
                    <Button variant="outline" size="sm">
                      Disabled
                    </Button>
                  </div>
                  <div className="flex items-center justify-between border-b border-gray-100 py-2">
                    <span className="text-gray-600">Session timeout</span>
                    <Button variant="outline" size="sm">
                      30 minutes
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
