"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

export default function AdminSettingsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [registrationOpen, setRegistrationOpen] = useState(true)
  const [abstractSubmissionOpen, setAbstractSubmissionOpen] = useState(true)

  const handleSave = async () => {
    setIsLoading(true)
    // 模拟保存操作
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Configure system-wide settings and preferences</p>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? (
            <>
              <i className="fas fa-spinner fa-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="fas fa-save mr-2"></i>
              Save All Changes
            </>
          )}
        </Button>
      </div>

      {/* 设置标签页 */}
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="conference">Conference</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="site-title">Site Title</Label>
                <Input id="site-title" defaultValue="International Forum on Maize Biology 2025" className="mt-1" />
              </div>
              <div>
                <Label htmlFor="site-description">Site Description</Label>
                <Textarea
                  id="site-description"
                  defaultValue="The premier international conference on maize biology research."
                  className="mt-1"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="contact-email">Contact Email</Label>
                <Input id="contact-email" type="email" defaultValue="<EMAIL>" className="mt-1" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="conference" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conference Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Registration Open</Label>
                  <p className="text-sm text-gray-500">Allow new user registrations</p>
                </div>
                <Switch checked={registrationOpen} onCheckedChange={setRegistrationOpen} />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Abstract Submission Open</Label>
                  <p className="text-sm text-gray-500">Allow abstract submissions</p>
                </div>
                <Switch checked={abstractSubmissionOpen} onCheckedChange={setAbstractSubmissionOpen} />
              </div>

              <div>
                <Label htmlFor="conference-dates">Conference Dates</Label>
                <Input id="conference-dates" defaultValue="October 16-20, 2025" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="venue">Venue</Label>
                <Input id="venue" defaultValue="Huazhong Agricultural University, Wuhan, China" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="registration-deadline">Registration Deadline</Label>
                <Input id="registration-deadline" type="date" defaultValue="2025-09-15" className="mt-1" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-gray-500">Send system notifications via email</p>
                </div>
                <Switch checked={emailNotifications} onCheckedChange={setEmailNotifications} />
              </div>

              <div>
                <Label htmlFor="smtp-server">SMTP Server</Label>
                <Input id="smtp-server" defaultValue="smtp.gmail.com" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="smtp-port">SMTP Port</Label>
                <Input id="smtp-port" type="number" defaultValue="587" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="from-email">From Email</Label>
                <Input id="from-email" type="email" defaultValue="<EMAIL>" className="mt-1" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                <Input id="session-timeout" type="number" defaultValue="60" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="max-login-attempts">Max Login Attempts</Label>
                <Input id="max-login-attempts" type="number" defaultValue="5" className="mt-1" />
              </div>

              <div className="pt-4">
                <Button variant="outline" className="w-full">
                  <i className="fas fa-key mr-2"></i>
                  Reset All User Sessions
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
