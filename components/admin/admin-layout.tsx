"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// User type definition
type User = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
}

// Sidebar menu items
const sidebarItems = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: "fas fa-tachometer-alt",
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: "fas fa-users",
  },
  {
    name: "Abstracts",
    href: "/admin/abstracts",
    icon: "fas fa-file-alt",
  },
  {
    name: "Payment Review",
    href: "/admin/payments",
    icon: "fas fa-credit-card",
  },
  {
    name: "Hotels",
    href: "/admin/hotels",
    icon: "fas fa-hotel",
  },
]

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [user, setUser] = useState<User | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(true) // 服务端渲染时的默认值
  const [isHydrated, setIsHydrated] = useState(false)
  const pathname = usePathname()

  // 客户端初始化和hydration处理
  useEffect(() => {
    // 在客户端hydration后，从localStorage恢复侧边栏状态
    try {
      const saved = localStorage.getItem("admin-sidebar-open")
      if (saved !== null) {
        const savedState = JSON.parse(saved) as boolean
        setSidebarOpen(savedState)
      }
    } catch {
      // 如果解析失败，保持默认值
    }

    setIsHydrated(true)
  }, [])

  // 当侧边栏状态改变时保存到localStorage
  const handleSidebarToggle = () => {
    // 只有在hydration完成后才允许切换
    if (!isHydrated) return

    const newState = !sidebarOpen
    setSidebarOpen(newState)
    if (typeof window !== "undefined") {
      localStorage.setItem("admin-sidebar-open", JSON.stringify(newState))
    }
  }

  useEffect(() => {
    // Check user permissions
    const userData = localStorage.getItem("user")

    if (userData) {
      try {
        // Add more robust JSON parsing error handling
        let parsedData
        try {
          parsedData = JSON.parse(userData) as unknown
        } catch (_jsonError) {
          // Invalid user data format, redirect to login page
          window.location.href = "/login"
          return
        }

        // Extract user information, support different data structures
        const parsedDataTyped = parsedData as Record<string, unknown>
        const userInfo =
          parsedDataTyped.user_info || (parsedDataTyped.data as Record<string, unknown>)?.user_info || parsedDataTyped
        const userInfoTyped = userInfo as Record<string, unknown>

        // Build standardized user object
        const roleValue = (userInfoTyped.role as string) || "user"
        const standardUser: User = {
          id: (userInfoTyped.id as string)?.toString() || "",
          name: (userInfoTyped.name as string) || "",
          email: (userInfoTyped.email as string) || "",
          role: roleValue === "admin" || roleValue === "user" ? roleValue : "user",
        }

        // Set user directly, check role during rendering
        setUser(standardUser)
      } catch (_error) {
        // Error processing user data, redirect to login page
        window.location.href = "/login"
        return
      }
    } else {
      // User data not found, redirect to login page
      window.location.href = "/login"
      return
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem("user")
    window.location.href = "/"
  }

  // Show loading state until user data is loaded
  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="mb-4 text-2xl font-bold text-gray-900">Loading...</h1>
            <p className="mb-4 text-gray-600">Verifying your access permissions</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Role check: if user is not admin, show access denied page
  if (user.role !== "admin") {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="mb-4 text-2xl font-bold text-red-600">Access Denied</h1>
            <p className="mb-4 text-gray-600">
              You do not have admin privileges to access this page. Your role is:{" "}
              <span className="font-bold">{user.role}</span>.
            </p>
            <div className="mt-6 flex flex-col justify-center space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <Link href="/dashboard">
                <Button variant="outline" className="w-full sm:w-auto">
                  Back to Dashboard
                </Button>
              </Link>
              <Button onClick={handleLogout} variant="destructive" className="w-full sm:w-auto">
                Logout
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <motion.div
        initial={isHydrated ? { x: -250 } : false}
        animate={isHydrated ? { x: sidebarOpen ? 0 : -200 } : false}
        transition={isHydrated ? { duration: 0.3 } : { duration: 0 }}
        className={`${
          sidebarOpen ? "w-64" : "w-16"
        } _duration-300 flex flex-col border-r border-gray-200 bg-white shadow-sm transition-all`}
      >
        {/* Sidebar Header */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <div>
                <h1 className="text-lg font-bold text-gray-900">Admin Panel</h1>
                <p className="text-sm text-gray-500">IFMB 2025</p>
              </div>
            )}
            <Button variant="ghost" size="sm" onClick={handleSidebarToggle} className="p-2">
              <i className={`fas ${sidebarOpen ? "fa-chevron-left" : "fa-chevron-right"}`}></i>
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={`flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                    pathname === item.href ? "bg-gray-900 text-white" : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <i className={`${item.icon} ${sidebarOpen ? "mr-3" : ""} w-5 text-center`}></i>
                  {sidebarOpen && <span>{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* User Info */}
        <div className="border-t border-gray-200 p-4">
          {sidebarOpen ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="truncate text-xs text-gray-500">{user.email}</p>
                </div>
              </div>
              <div className="space-y-1">
                <Link href="/" className="block">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <i className="fas fa-home mr-2"></i>
                    Back to Site
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="w-full justify-start text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <i className="fas fa-sign-out-alt mr-2"></i>
                  Logout
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="mx-auto flex h-8 w-8 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                {user.name.charAt(0).toUpperCase()}
              </div>
              <Link href="/" className="block">
                <Button variant="ghost" size="sm" className="w-full p-2">
                  <i className="fas fa-home"></i>
                </Button>
              </Link>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full p-2 text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                <i className="fas fa-sign-out-alt"></i>
              </Button>
            </div>
          )}
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Top Bar */}
        <div className="border-b border-gray-200 bg-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {sidebarItems.find((item) => item.href === pathname)?.name || "Admin Panel"}
              </h2>
              <p className="text-sm text-gray-500">Manage your International Forum on Maize Biology 2025</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <i className="fas fa-download mr-2"></i>
                Export Data
              </Button>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  )
}
