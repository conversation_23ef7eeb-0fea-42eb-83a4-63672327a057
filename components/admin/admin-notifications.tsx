"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

// 模拟通知数据
type Notification = {
  id: string
  title: string
  content: string
  type: "info" | "warning" | "success" | "error"
  status: "draft" | "published" | "archived"
  targetAudience: "all" | "users" | "speakers" | "admins"
  createdAt: string
  publishedAt?: string
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "Conference Registration Now Open",
    content: "Registration for IFMB 2025 is now open. Early bird pricing available until March 31st.",
    type: "info",
    status: "published",
    targetAudience: "all",
    createdAt: "2024-01-15",
    publishedAt: "2024-01-15",
  },
  {
    id: "2",
    title: "Abstract Submission Deadline Extended",
    content: "Due to popular demand, we have extended the abstract submission deadline to April 15th.",
    type: "warning",
    status: "published",
    targetAudience: "users",
    createdAt: "2024-01-20",
    publishedAt: "2024-01-20",
  },
  {
    id: "3",
    title: "New Speaker Announcement",
    content: "We are excited to announce Dr. Jane <PERSON> as our keynote speaker for the opening ceremony.",
    type: "success",
    status: "draft",
    targetAudience: "all",
    createdAt: "2024-01-25",
  },
]

export default function AdminNotificationsPage() {
  const [notifications, setNotifications] = useState(mockNotifications)
  const [searchTerm, setSearchTerm] = useState("")
  const [newNotification, setNewNotification] = useState<{
    title: string
    content: string
    type: "info" | "warning" | "success" | "error"
    targetAudience: "all" | "users" | "speakers" | "admins"
  }>({
    title: "",
    content: "",
    type: "info",
    targetAudience: "all",
  })

  const filteredNotifications = notifications.filter(
    (notification) =>
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateNotification = () => {
    if (!newNotification.title || !newNotification.content) return

    const notification: Notification = {
      id: Date.now().toString(),
      ...newNotification,
      status: "draft",
      createdAt: new Date().toISOString().split("T")[0] as string,
    }

    setNotifications([notification, ...notifications])
    setNewNotification({
      title: "",
      content: "",
      type: "info",
      targetAudience: "all",
    })
  }

  const handlePublishNotification = (id: string) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id
          ? {
              ...notification,
              status: "published" as const,
              publishedAt: new Date().toISOString().split("T")[0] as string,
            }
          : notification
      )
    )
  }

  const handleArchiveNotification = (id: string) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, status: "archived" as const } : notification
      )
    )
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "info":
        return "bg-blue-50 text-blue-700 border-blue-200"
      case "warning":
        return "bg-amber-50 text-amber-700 border-amber-200"
      case "success":
        return "bg-green-50 text-green-700 border-green-200"
      case "error":
        return "bg-red-50 text-red-700 border-red-200"
      default:
        return "bg-gray-50 text-gray-700 border-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-50 text-green-700 border-green-200"
      case "draft":
        return "bg-gray-50 text-gray-700 border-gray-200"
      case "archived":
        return "bg-red-50 text-red-700 border-red-200"
      default:
        return "bg-gray-50 text-gray-700 border-gray-200"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notification Management</h1>
          <p className="text-gray-600">Create and manage system notifications</p>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">All Notifications</TabsTrigger>
          <TabsTrigger value="create">Create New</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Search */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <i className="fas fa-filter mr-2"></i>
                  Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Notifications List */}
          <Card>
            <CardHeader>
              <CardTitle>All Notifications ({filteredNotifications.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <div key={notification.id} className="rounded-lg border border-gray-200 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center space-x-2">
                          <h3 className="font-semibold text-gray-900">{notification.title}</h3>
                          <Badge variant="outline" className={getTypeColor(notification.type)}>
                            {notification.type}
                          </Badge>
                          <Badge variant="outline" className={getStatusColor(notification.status)}>
                            {notification.status}
                          </Badge>
                        </div>
                        <p className="mb-2 text-gray-600">{notification.content}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Target: {notification.targetAudience}</span>
                          <span>Created: {notification.createdAt}</span>
                          {notification.publishedAt && <span>Published: {notification.publishedAt}</span>}
                        </div>
                      </div>
                      <div className="ml-4 flex space-x-2">
                        {notification.status === "draft" && (
                          <Button size="sm" onClick={() => handlePublishNotification(notification.id)}>
                            <i className="fas fa-paper-plane mr-1"></i>
                            Publish
                          </Button>
                        )}
                        {notification.status === "published" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleArchiveNotification(notification.id)}
                          >
                            <i className="fas fa-archive mr-1"></i>
                            Archive
                          </Button>
                        )}
                        <Button variant="ghost" size="sm">
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button variant="ghost" size="sm">
                          <i className="fas fa-trash text-red-500"></i>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create New Notification</CardTitle>
              <CardDescription>Create a new system notification for users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">Title</label>
                <Input
                  placeholder="Notification title..."
                  value={newNotification.title}
                  onChange={(e) => setNewNotification({ ...newNotification, title: e.target.value })}
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">Content</label>
                <Textarea
                  placeholder="Notification content..."
                  value={newNotification.content}
                  onChange={(e) => setNewNotification({ ...newNotification, content: e.target.value })}
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">Type</label>
                  <Select
                    value={newNotification.type}
                    onValueChange={(value) =>
                      setNewNotification({
                        ...newNotification,
                        type: value as "info" | "warning" | "success" | "error",
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">Target Audience</label>
                  <Select
                    value={newNotification.targetAudience}
                    onValueChange={(value) =>
                      setNewNotification({
                        ...newNotification,
                        targetAudience: value as "all" | "users" | "speakers" | "admins",
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="users">Regular Users</SelectItem>
                      <SelectItem value="speakers">Speakers</SelectItem>
                      <SelectItem value="admins">Admins</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-4 pt-4">
                <Button onClick={handleCreateNotification}>
                  <i className="fas fa-save mr-2"></i>
                  Save as Draft
                </Button>
                <Button variant="outline">
                  <i className="fas fa-paper-plane mr-2"></i>
                  Save and Publish
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
