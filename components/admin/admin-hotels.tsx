"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

// 酒店数据类型
type Hotel = {
  id: number
  name: string
  chineseName: string
  address: string
  phone: string
  email: string
  rating: number
  imageUrl: string
  description: string
  amenities: string[]
  coordinates: {
    lat: number
    lng: number
  }
  status: "active" | "inactive"
}

// 房间类型数据
type RoomType = {
  id: number
  hotelId: number
  hotelName: string
  type: string
  description: string
  capacity: number
  price: number
  totalRooms: number
  availableRooms: number
  amenities: string[]
  images: string[]
}

// 模拟酒店数据
const mockHotels: Hotel[] = [
  {
    id: 1,
    name: "Grand Conference Hotel",
    chineseName: "大会议酒店",
    address: "123 University Avenue, Wuhan, Hubei",
    phone: "+86-27-8888-8888",
    email: "<EMAIL>",
    rating: 5,
    imageUrl: "https://minioapi.bugmaker.me/ifmb-2025-public/hotel1.jpg",
    description: "Luxury hotel with excellent conference facilities",
    amenities: ["WiFi", "Restaurant", "Gym", "Pool", "Conference Rooms"],
    coordinates: { lat: 30.5928, lng: 114.3055 },
    status: "active",
  },
  {
    id: 2,
    name: "University Guest House",
    chineseName: "大学宾馆",
    address: "456 Campus Road, Wuhan, Hubei",
    phone: "+86-27-6666-6666",
    email: "<EMAIL>",
    rating: 4,
    imageUrl: "https://minioapi.bugmaker.me/ifmb-2025-public/hotel2.jpg",
    description: "Comfortable accommodation on campus",
    amenities: ["WiFi", "Restaurant", "Parking"],
    coordinates: { lat: 30.5828, lng: 114.2955 },
    status: "active",
  },
]

// 模拟房间类型数据
const mockRoomTypes: RoomType[] = [
  {
    id: 1,
    hotelId: 1,
    hotelName: "Grand Conference Hotel",
    type: "Standard Single",
    description: "Comfortable single room with modern amenities",
    capacity: 1,
    price: 280,
    totalRooms: 50,
    availableRooms: 35,
    amenities: ["WiFi", "Air Conditioning", "TV", "Mini Bar"],
    images: ["room1.jpg", "room1_bathroom.jpg"],
  },
  {
    id: 2,
    hotelId: 1,
    hotelName: "Grand Conference Hotel",
    type: "Standard Double",
    description: "Spacious double room perfect for couples",
    capacity: 2,
    price: 380,
    totalRooms: 40,
    availableRooms: 28,
    amenities: ["WiFi", "Air Conditioning", "TV", "Mini Bar", "Balcony"],
    images: ["room2.jpg", "room2_bathroom.jpg"],
  },
  {
    id: 3,
    hotelId: 2,
    hotelName: "University Guest House",
    type: "Economy Single",
    description: "Budget-friendly single room",
    capacity: 1,
    price: 180,
    totalRooms: 30,
    availableRooms: 22,
    amenities: ["WiFi", "Air Conditioning", "TV"],
    images: ["room3.jpg"],
  },
]

export default function AdminHotelsPage() {
  const [hotels, setHotels] = useState<Hotel[]>(mockHotels)
  const [roomTypes, setRoomTypes] = useState<RoomType[]>(mockRoomTypes)
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null)
  const [isAddingHotel, setIsAddingHotel] = useState(false)
  const [isAddingRoom, setIsAddingRoom] = useState(false)

  // 新酒店表单数据
  const [newHotel, setNewHotel] = useState<Partial<Hotel>>({
    name: "",
    chineseName: "",
    address: "",
    phone: "",
    email: "",
    rating: 4,
    description: "",
    amenities: [],
    status: "active",
  })

  // 新房间类型表单数据
  const [newRoomType, setNewRoomType] = useState<Partial<RoomType>>({
    hotelId: 0,
    type: "",
    description: "",
    capacity: 1,
    price: 0,
    totalRooms: 0,
    amenities: [],
  })

  // 添加酒店
  const handleAddHotel = () => {
    if (newHotel.name && newHotel.address) {
      const hotel: Hotel = {
        id: Date.now(),
        name: newHotel.name,
        chineseName: newHotel.chineseName || "",
        address: newHotel.address,
        phone: newHotel.phone || "",
        email: newHotel.email || "",
        rating: newHotel.rating || 4,
        imageUrl: "https://minioapi.bugmaker.me/ifmb-2025-public/default-hotel.jpg",
        description: newHotel.description || "",
        amenities: newHotel.amenities || [],
        coordinates: { lat: 30.5928, lng: 114.3055 },
        status: newHotel.status || "active",
      }
      setHotels([...hotels, hotel])
      setNewHotel({
        name: "",
        chineseName: "",
        address: "",
        phone: "",
        email: "",
        rating: 4,
        description: "",
        amenities: [],
        status: "active",
      })
      setIsAddingHotel(false)
    }
  }

  // 添加房间类型
  const handleAddRoomType = () => {
    if (newRoomType.hotelId && newRoomType.type && newRoomType.price) {
      const selectedHotelData = hotels.find((h) => h.id === newRoomType.hotelId)
      const roomType: RoomType = {
        id: Date.now(),
        hotelId: newRoomType.hotelId,
        hotelName: selectedHotelData?.name || "",
        type: newRoomType.type,
        description: newRoomType.description || "",
        capacity: newRoomType.capacity || 1,
        price: newRoomType.price,
        totalRooms: newRoomType.totalRooms || 0,
        availableRooms: newRoomType.totalRooms || 0,
        amenities: newRoomType.amenities || [],
        images: [],
      }
      setRoomTypes([...roomTypes, roomType])
      setNewRoomType({
        hotelId: 0,
        type: "",
        description: "",
        capacity: 1,
        price: 0,
        totalRooms: 0,
        amenities: [],
      })
      setIsAddingRoom(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hotel Management</h1>
          <p className="text-gray-600">Manage hotels and room configurations for IFMB 2025</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddingHotel(true)}>
            <i className="fas fa-plus mr-2"></i>
            Add Hotel
          </Button>
          <Button variant="outline" onClick={() => setIsAddingRoom(true)}>
            <i className="fas fa-bed mr-2"></i>
            Add Room Type
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Hotels</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{hotels.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-hotel text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Room Types</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{roomTypes.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-bed text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + rt.totalRooms, 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <i className="fas fa-door-open text-xl text-purple-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Available Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + rt.availableRooms, 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                <i className="fas fa-key text-xl text-orange-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容标签页 */}
      <Tabs defaultValue="hotels" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="hotels">Hotels</TabsTrigger>
          <TabsTrigger value="rooms">Room Types</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="merging">Room Merging</TabsTrigger>
        </TabsList>

        {/* 酒店管理 */}
        <TabsContent value="hotels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hotel List</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {hotels.map((hotel) => (
                  <div key={hotel.id} className="rounded-lg border border-gray-200 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900">{hotel.name}</h3>
                          <Badge
                            className={
                              hotel.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                            }
                          >
                            {hotel.status === "active" ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        <p className="mb-1 text-sm text-gray-600">{hotel.chineseName}</p>
                        <p className="mb-2 text-sm text-gray-600">{hotel.address}</p>
                        <div className="mb-2 flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <i
                              key={i}
                              className={`fas fa-star text-sm ${
                                i < hotel.rating ? "text-yellow-400" : "text-gray-300"
                              }`}
                            />
                          ))}
                          <span className="ml-1 text-sm text-gray-600">({hotel.rating}/5)</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {hotel.amenities.slice(0, 3).map((amenity) => (
                            <Badge key={amenity} variant="outline" className="text-xs">
                              {amenity}
                            </Badge>
                          ))}
                          {hotel.amenities.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{hotel.amenities.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="ml-4 flex gap-2">
                        <Button size="sm" variant="outline">
                          <i className="fas fa-edit mr-1"></i>
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          <i className="fas fa-eye mr-1"></i>
                          View
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间类型管理 */}
        <TabsContent value="rooms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roomTypes.map((roomType) => (
                  <div key={roomType.id} className="rounded-lg border border-gray-200 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900">{roomType.type}</h3>
                          <Badge variant="outline">{roomType.hotelName}</Badge>
                        </div>
                        <p className="mb-2 text-sm text-gray-600">{roomType.description}</p>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 md:grid-cols-4">
                          <div>
                            <span className="font-medium">Capacity:</span> {roomType.capacity} person(s)
                          </div>
                          <div>
                            <span className="font-medium">Price:</span> ¥{roomType.price}/night
                          </div>
                          <div>
                            <span className="font-medium">Total Rooms:</span> {roomType.totalRooms}
                          </div>
                          <div>
                            <span className="font-medium">Available:</span> {roomType.availableRooms}
                          </div>
                        </div>
                        <div className="mt-2 flex flex-wrap gap-1">
                          {roomType.amenities.map((amenity) => (
                            <Badge key={amenity} variant="outline" className="text-xs">
                              {amenity}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="ml-4 flex gap-2">
                        <Button size="sm" variant="outline">
                          <i className="fas fa-edit mr-1"></i>
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          <i className="fas fa-chart-bar mr-1"></i>
                          Stats
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 预订管理 */}
        <TabsContent value="bookings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <i className="fas fa-calendar-check mb-4 text-4xl text-gray-400"></i>
                <h3 className="mb-2 text-lg font-medium text-gray-900">Booking Management</h3>
                <p className="mb-4 text-gray-600">View and manage all room bookings for the conference</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间合并 */}
        <TabsContent value="merging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Merging</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <i className="fas fa-object-group mb-4 text-4xl text-gray-400"></i>
                <h3 className="mb-2 text-lg font-medium text-gray-900">Room Merging Management</h3>
                <p className="mb-4 text-gray-600">Manage room sharing and merging requests</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加酒店对话框 */}
      {isAddingHotel && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
          <div className="w-full max-w-md rounded-lg bg-white p-6">
            <h3 className="mb-4 text-lg font-semibold">Add New Hotel</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="hotel-name">Hotel Name</Label>
                <Input
                  id="hotel-name"
                  value={newHotel.name}
                  onChange={(e) => setNewHotel({ ...newHotel, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-chinese-name">Chinese Name</Label>
                <Input
                  id="hotel-chinese-name"
                  value={newHotel.chineseName}
                  onChange={(e) => setNewHotel({ ...newHotel, chineseName: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-address">Address</Label>
                <Textarea
                  id="hotel-address"
                  value={newHotel.address}
                  onChange={(e) => setNewHotel({ ...newHotel, address: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-phone">Phone</Label>
                <Input
                  id="hotel-phone"
                  value={newHotel.phone}
                  onChange={(e) => setNewHotel({ ...newHotel, phone: e.target.value })}
                />
              </div>
            </div>
            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddHotel}>Add Hotel</Button>
              <Button variant="outline" onClick={() => setIsAddingHotel(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
