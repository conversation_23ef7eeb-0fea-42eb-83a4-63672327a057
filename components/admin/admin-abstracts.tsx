"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Abstract submission data type
type AbstractSubmission = {
  id: number
  title: string
  author: string
  email: string
  organization: string
  theme: string
  submissionDate: string
  status: "submitted" | "approved" | "rejected"
  presentationType: "oral" | "poster" | "both"
  publicationStatus: "published" | "unpublished"
  fileName: string
}

// Mock abstract data
const mockAbstracts: AbstractSubmission[] = [
  {
    id: 1,
    title: "Genetic Analysis of Drought Tolerance in Maize Varieties",
    author: "Dr. <PERSON>",
    email: "<EMAIL>",
    organization: "University of Agriculture",
    theme: "Genetics and Genomics",
    submissionDate: "2024-12-15",
    status: "submitted",
    presentationType: "oral",
    publicationStatus: "unpublished",
    fileName: "drought_tolerance_analysis.pdf",
  },
  {
    id: 2,
    title: "Impact of Climate Change on Maize Production in Sub-Saharan Africa",
    author: "Prof. <PERSON>",
    email: "<EMAIL>",
    organization: "International Research Institute",
    theme: "Climate and Environment",
    submissionDate: "2024-12-14",
    status: "approved",
    presentationType: "poster",
    publicationStatus: "published",
    fileName: "climate_impact_maize.pdf",
  },
  {
    id: 3,
    title: "Novel Breeding Techniques for Enhanced Nutritional Content",
    author: "Dr. Emily Rodriguez",
    email: "<EMAIL>",
    organization: "BioTech Solutions",
    theme: "Breeding and Biotechnology",
    submissionDate: "2024-12-13",
    status: "submitted",
    presentationType: "both",
    publicationStatus: "unpublished",
    fileName: "breeding_nutrition.pdf",
  },
  {
    id: 4,
    title: "Molecular Markers for Disease Resistance in Maize",
    author: "Dr. James Wilson",
    email: "<EMAIL>",
    organization: "Genetics Research Center",
    theme: "Genetics and Genomics",
    submissionDate: "2024-12-12",
    status: "rejected",
    presentationType: "oral",
    publicationStatus: "published",
    fileName: "molecular_markers.pdf",
  },
  {
    id: 5,
    title: "Sustainable Farming Practices for Maize Cultivation",
    author: "Dr. Lisa Thompson",
    email: "<EMAIL>",
    organization: "Sustainable Agriculture Foundation",
    theme: "Sustainable Agriculture",
    submissionDate: "2024-12-11",
    status: "approved",
    presentationType: "poster",
    publicationStatus: "unpublished",
    fileName: "sustainable_farming.pdf",
  },
]

export default function AdminAbstractsPage() {
  const [abstracts] = useState<AbstractSubmission[]>(mockAbstracts)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [themeFilter, setThemeFilter] = useState<string>("all")

  // Filter abstracts
  const filteredAbstracts = abstracts.filter((abstract) => {
    const matchesSearch =
      abstract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.organization.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || abstract.status === statusFilter
    const matchesTheme = themeFilter === "all" || abstract.theme === themeFilter

    return matchesSearch && matchesStatus && matchesTheme
  })

  // Statistics
  const stats = {
    total: abstracts.length,
    submitted: abstracts.filter((a) => a.status === "submitted").length,
    approved: abstracts.filter((a) => a.status === "approved").length,
    rejected: abstracts.filter((a) => a.status === "rejected").length,
  }

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "approved":
        return "bg-green-100 text-green-800 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  // Status display text
  const getStatusText = (status: string) => {
    switch (status) {
      case "submitted":
        return "Submitted"
      case "approved":
        return "Approved"
      case "rejected":
        return "Rejected"
      default:
        return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Submissions</h1>
          <p className="text-gray-600">View and download abstract submissions for IFMB 2025</p>
        </div>
        <Button>
          <i className="fas fa-download mr-2"></i>
          Export All
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Submissions</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-file-alt text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Submitted</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.submitted}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                <i className="fas fa-clock text-xl text-yellow-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Approved</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.approved}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-check-circle text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Rejected</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{stats.rejected}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <i className="fas fa-times-circle text-xl text-red-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="flex-1">
              <Input
                placeholder="Search by title, author, or organization..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select value={themeFilter} onValueChange={setThemeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Themes</SelectItem>
                  <SelectItem value="Genetics and Genomics">Genetics and Genomics</SelectItem>
                  <SelectItem value="Climate and Environment">Climate and Environment</SelectItem>
                  <SelectItem value="Breeding and Biotechnology">Breeding and Biotechnology</SelectItem>
                  <SelectItem value="Sustainable Agriculture">Sustainable Agriculture</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Abstract List */}
      <Card>
        <CardHeader>
          <CardTitle>Abstract Submissions ({filteredAbstracts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredAbstracts.map((abstract) => (
              <div key={abstract.id} className="rounded-lg border border-gray-200 p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-2 flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900">{abstract.title}</h3>
                      <Badge className={getStatusColor(abstract.status)}>{getStatusText(abstract.status)}</Badge>
                    </div>
                    <div className="grid grid-cols-1 gap-2 text-sm text-gray-600 md:grid-cols-2">
                      <div>
                        <span className="font-medium">Author:</span> {abstract.author}
                      </div>
                      <div>
                        <span className="font-medium">Organization:</span> {abstract.organization}
                      </div>
                      <div>
                        <span className="font-medium">Theme:</span> {abstract.theme}
                      </div>
                      <div>
                        <span className="font-medium">Submission Date:</span> {abstract.submissionDate}
                      </div>
                      <div>
                        <span className="font-medium">Presentation Type:</span>{" "}
                        {abstract.presentationType === "oral"
                          ? "Oral Presentation"
                          : abstract.presentationType === "poster"
                            ? "Poster Presentation"
                            : "Oral + Poster Presentation"}
                      </div>
                      <div>
                        <span className="font-medium">Publication Status:</span>{" "}
                        {abstract.publicationStatus === "published" ? "Published" : "Unpublished"}
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 flex gap-2">
                    <Button size="sm" variant="outline">
                      <i className="fas fa-download mr-1"></i>
                      Download
                    </Button>
                    <Button size="sm" variant="outline">
                      <i className="fas fa-eye mr-1"></i>
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
