"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// 摘要数据类型
type AbstractSubmission = {
  id: number
  title: string
  author: string
  email: string
  organization: string
  theme: string
  submissionDate: string
  status: "pending" | "approved" | "rejected" | "under_review"
  presentationType: "oral" | "poster" | "both"
  publicationStatus: "published" | "unpublished"
  fileName: string
}

// 模拟摘要数据
const mockAbstracts: AbstractSubmission[] = [
  {
    id: 1,
    title: "Genetic Analysis of Drought Tolerance in Maize Varieties",
    author: "Dr. <PERSON>",
    email: "<EMAIL>",
    organization: "University of Agriculture",
    theme: "Genetics and Genomics",
    submissionDate: "2024-12-15",
    status: "pending",
    presentationType: "oral",
    publicationStatus: "unpublished",
    fileName: "drought_tolerance_analysis.pdf",
  },
  {
    id: 2,
    title: "Impact of Climate Change on Maize Production in Sub-Saharan Africa",
    author: "Prof. Michael Chen",
    email: "<EMAIL>",
    organization: "International Research Institute",
    theme: "Climate and Environment",
    submissionDate: "2024-12-14",
    status: "approved",
    presentationType: "poster",
    publicationStatus: "published",
    fileName: "climate_impact_maize.pdf",
  },
  {
    id: 3,
    title: "Novel Breeding Techniques for Enhanced Nutritional Content",
    author: "Dr. Emily Rodriguez",
    email: "<EMAIL>",
    organization: "BioTech Solutions",
    theme: "Breeding and Biotechnology",
    submissionDate: "2024-12-13",
    status: "under_review",
    presentationType: "both",
    publicationStatus: "unpublished",
    fileName: "breeding_nutrition.pdf",
  },
  {
    id: 4,
    title: "Molecular Markers for Disease Resistance in Maize",
    author: "Dr. James Wilson",
    email: "<EMAIL>",
    organization: "Genetics Research Center",
    theme: "Genetics and Genomics",
    submissionDate: "2024-12-12",
    status: "rejected",
    presentationType: "oral",
    publicationStatus: "published",
    fileName: "molecular_markers.pdf",
  },
  {
    id: 5,
    title: "Sustainable Farming Practices for Maize Cultivation",
    author: "Dr. Lisa Thompson",
    email: "<EMAIL>",
    organization: "Sustainable Agriculture Foundation",
    theme: "Sustainable Agriculture",
    submissionDate: "2024-12-11",
    status: "approved",
    presentationType: "poster",
    publicationStatus: "unpublished",
    fileName: "sustainable_farming.pdf",
  },
]

export default function AdminAbstractsPage() {
  const [abstracts, setAbstracts] = useState<AbstractSubmission[]>(mockAbstracts)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [themeFilter, setThemeFilter] = useState<string>("all")

  // 过滤摘要
  const filteredAbstracts = abstracts.filter((abstract) => {
    const matchesSearch =
      abstract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.organization.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || abstract.status === statusFilter
    const matchesTheme = themeFilter === "all" || abstract.theme === themeFilter

    return matchesSearch && matchesStatus && matchesTheme
  })

  // 统计数据
  const stats = {
    total: abstracts.length,
    pending: abstracts.filter((a) => a.status === "pending").length,
    approved: abstracts.filter((a) => a.status === "approved").length,
    rejected: abstracts.filter((a) => a.status === "rejected").length,
    underReview: abstracts.filter((a) => a.status === "under_review").length,
  }

  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "approved":
        return "bg-green-100 text-green-800 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200"
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  // 状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "待审核"
      case "approved":
        return "已通过"
      case "rejected":
        return "已拒绝"
      case "under_review":
        return "审核中"
      default:
        return status
    }
  }

  // 处理状态更新
  const handleStatusUpdate = (id: number, newStatus: AbstractSubmission["status"]) => {
    setAbstracts((prev) => prev.map((abstract) => (abstract.id === id ? { ...abstract, status: newStatus } : abstract)))
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Management</h1>
          <p className="text-gray-600">Review and manage abstract submissions for IFMB 2025</p>
        </div>
        <Button>
          <i className="fas fa-download mr-2"></i>
          Export Data
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-500">Total</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-500">Under Review</p>
              <p className="text-2xl font-bold text-blue-600">{stats.underReview}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-500">Rejected</p>
              <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 过滤器 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="flex-1">
              <Input
                placeholder="搜索摘要标题、作者或机构..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="pending">待审核</SelectItem>
                  <SelectItem value="under_review">审核中</SelectItem>
                  <SelectItem value="approved">已通过</SelectItem>
                  <SelectItem value="rejected">已拒绝</SelectItem>
                </SelectContent>
              </Select>
              <Select value={themeFilter} onValueChange={setThemeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="主题筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有主题</SelectItem>
                  <SelectItem value="Genetics and Genomics">遗传学与基因组学</SelectItem>
                  <SelectItem value="Climate and Environment">气候与环境</SelectItem>
                  <SelectItem value="Breeding and Biotechnology">育种与生物技术</SelectItem>
                  <SelectItem value="Sustainable Agriculture">可持续农业</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 摘要列表 */}
      <Card>
        <CardHeader>
          <CardTitle>Abstract Submissions ({filteredAbstracts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredAbstracts.map((abstract) => (
              <div key={abstract.id} className="rounded-lg border border-gray-200 p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-2 flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900">{abstract.title}</h3>
                      <Badge className={getStatusColor(abstract.status)}>{getStatusText(abstract.status)}</Badge>
                    </div>
                    <div className="grid grid-cols-1 gap-2 text-sm text-gray-600 md:grid-cols-2">
                      <div>
                        <span className="font-medium">作者:</span> {abstract.author}
                      </div>
                      <div>
                        <span className="font-medium">机构:</span> {abstract.organization}
                      </div>
                      <div>
                        <span className="font-medium">主题:</span> {abstract.theme}
                      </div>
                      <div>
                        <span className="font-medium">提交日期:</span> {abstract.submissionDate}
                      </div>
                      <div>
                        <span className="font-medium">展示类型:</span>{" "}
                        {abstract.presentationType === "oral"
                          ? "口头报告"
                          : abstract.presentationType === "poster"
                            ? "海报展示"
                            : "口头报告 + 海报展示"}
                      </div>
                      <div>
                        <span className="font-medium">发表状态:</span>{" "}
                        {abstract.publicationStatus === "published" ? "已发表" : "未发表"}
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 flex gap-2">
                    {abstract.status === "pending" && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(abstract.id, "under_review")}
                        >
                          开始审核
                        </Button>
                        <Button size="sm" onClick={() => handleStatusUpdate(abstract.id, "approved")}>
                          通过
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleStatusUpdate(abstract.id, "rejected")}
                        >
                          拒绝
                        </Button>
                      </>
                    )}
                    {abstract.status === "under_review" && (
                      <>
                        <Button size="sm" onClick={() => handleStatusUpdate(abstract.id, "approved")}>
                          通过
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleStatusUpdate(abstract.id, "rejected")}
                        >
                          拒绝
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="outline">
                      <i className="fas fa-download mr-1"></i>
                      下载
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
